services:
  postgres:
    image: postgres:15
    container_name: timesheet_db
    environment:
      POSTGRES_DB: timesheet
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    ports:
    # Port 5433: Docker
    #Port 5432: Local PostgreSQL
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data: