// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String      @id @default(cuid())
  name             String
  email            String      @unique
  password         String
  role             UserRole
  desk             String?
  hourlyRate       Decimal?    @db.Decimal(10, 2)
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt
  
  managedProjects  Project[]   @relation("ProjectManager")
  assignments      Assignment[]
  timeEntries      TimeEntry[]
  approvedEntries  TimeEntry[] @relation("ApprovedBy")

  @@map("users")
}

model Project {
  id           String        @id @default(cuid())
  name         String
  description  String?
  startDate    DateTime
  endDate      DateTime?
  status       ProjectStatus @default(NOT_STARTED)
  managerId    String
  budget       Decimal?      @db.Decimal(12, 2)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  
  manager      User          @relation("ProjectManager", fields: [managerId], references: [id])
  deliverables Deliverable[]

  @@map("projects")
}

model Deliverable {
  id             String            @id @default(cuid())
  projectId      String
  title          String
  description    String?
  dueDate        DateTime?
  status         DeliverableStatus @default(NOT_STARTED)
  estimatedHours Int?
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  
  project        Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  assignments    Assignment[]

  @@map("deliverables")
}

model Assignment {
  id             String           @id @default(cuid())
  deliverableId  String
  userId         String
  assignedDate   DateTime         @default(now())
  status         AssignmentStatus @default(ACTIVE)
  estimatedHours Int?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  
  deliverable    Deliverable      @relation(fields: [deliverableId], references: [id], onDelete: Cascade)
  user           User             @relation(fields: [userId], references: [id])
  timeEntries    TimeEntry[]

  @@unique([deliverableId, userId])
  @@map("assignments")
}

model TimeEntry {
  id           String          @id @default(cuid())
  assignmentId String
  userId       String
  date         DateTime        @db.Date
  hours        Decimal         @db.Decimal(4, 2)
  comments     String?
  status       TimeEntryStatus @default(SUBMITTED)
  submittedAt  DateTime        @default(now())
  approvedAt   DateTime?
  approvedById String?
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  
  assignment   Assignment      @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  user         User            @relation(fields: [userId], references: [id])
  approvedBy   User?           @relation("ApprovedBy", fields: [approvedById], references: [id])

  @@map("time_entries")
}

enum UserRole {
  ADMIN
  PROJECT_MANAGER
  TEAM_MEMBER
}

enum ProjectStatus {
  NOT_STARTED
  IN_PROGRESS
  ON_HOLD
  COMPLETED
}

enum DeliverableStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
}

enum AssignmentStatus {
  ACTIVE
  COMPLETED
  REASSIGNED
}

enum TimeEntryStatus {
  SUBMITTED
  APPROVED
  REJECTED
}