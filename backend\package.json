{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@apollo/server": "^4.12.2", "@nestjs/apollo": "^13.1.0", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@prisma/client": "^6.13.0", "apollo-server-express": "^3.13.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "graphql": "^16.11.0", "graphql-subscriptions": "^3.0.0", "graphql-upload": "^17.0.0", "graphql-ws": "^6.0.6", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "type-graphql": "^2.0.0-rc.2"}, "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/multer": "^2.0.0", "@types/node": "^24.2.0", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "prettier": "^3.6.2", "prisma": "^6.13.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}