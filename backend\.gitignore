
# ===================================
# NODE.JS & NPM
# ===================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Package manager lock files (choose one to keep)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# ESLint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# ===================================
# NESTJS SPECIFIC
# ===================================

# Compiled output
/dist/
/build/
/out/
*.js.map

# NestJS build artifacts
.nest/

# Auto-generated GraphQL schema files
schema.gql
schema.graphql

# TypeScript cache
*.tsbuildinfo

# ===================================
# TYPESCRIPT
# ===================================

# TypeScript compiled files
*.js
*.d.ts
!jest.config.js
!nest-cli.js
!.eslintrc.js
!webpack.config.js

# TypeScript incremental compilation
*.tsbuildinfo

# ===================================
# DATABASE & PRISMA
# ===================================

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Prisma
/prisma/migrations/
/prisma/dev.db*
prisma/.env
/generated/prisma

# Database backup files
*.sql
*.bak
*.dump

# ===================================
# TESTING
# ===================================

# Jest
jest.config.js
coverage/
*.coverage
.nyc_output/

# Testing artifacts
test-results/
playwright-report/
test-results.xml

# ===================================
# DEVELOPMENT TOOLS
# ===================================

# IDE and Editor files
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
.vim/
*.un~
Session.vim

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===================================
# OPERATING SYSTEMS
# ===================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# LOGS
# ===================================

# Application logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime logs
pids/
*.pid
*.seed
*.pid.lock

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# ===================================
# DOCKER
# ===================================

# Docker
.dockerignore
docker-compose.override.yml
.docker/

# ===================================
# SECURITY & SECRETS
# ===================================

# Certificates and keys
*.pem
*.key
*.crt
*.cer
*.p12
*.pfx

# JWT secrets and other sensitive files
jwt-private.key
jwt-public.key
.secrets/

# ===================================
# UPLOADS & TEMPORARY FILES
# ===================================

# File uploads
uploads/
tmp/
temp/
.tmp/

# Image processing cache
.cache/

# ===================================
# DOCUMENTATION
# ===================================

# Generated documentation
docs/build/
.docusaurus/

# ===================================
# DEPLOYMENT & CI/CD
# ===================================

# Deployment files
.vercel/
.netlify/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# AWS
.aws/

# GitHub Actions artifacts
.github/workflows/secrets/

# ===================================
# MONITORING & PROFILING
# ===================================

# Performance monitoring
newrelic_agent.log
.newrelic/

# Profiling
*.prof
*.cpuprofile
*.heapsnapshot

# ===================================
# BACKUP FILES
# ===================================

# Backup files
*.backup
*.bak
*.old
*.orig

# ===================================
# MISC
# ===================================

# Webpack
.webpack/

# Parcel
.parcel-cache/

# Next.js (if using hybrid setup)
.next/

# SvelteKit (if using hybrid setup)
.svelte-kit/

# Vite
dist-ssr/
*.local

# Serverless Framework
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test/

# Temporary folders
.tmp/
.temp/

# Local configuration files that shouldn't be shared
config.local.json
settings.local.json

# GraphQL code generation
src/**/*.generated.ts

# Auto-generated Prisma files (keep only schema.prisma)
# Uncomment if you want to exclude generated migrations
# prisma/migrations/

# Swagger/OpenAPI generated files
swagger-ui/
api-docs/

# ===================================
# PROJECT SPECIFIC
# ===================================

# Timesheet specific uploads
/uploads/timesheets/
/uploads/attachments/

# Generated reports
/reports/generated/
/exports/

# Cache directories
.cache/
.parcel-cache/

# Development database
dev.db

# Test fixtures
test/fixtures/generated/

# ===================================
# KEEP THESE FILES (NEGATED)
# ===================================

# Keep essential config files
!.gitignore
!.env.example
!.env.template
!README.md
!LICENSE
!package.json
!tsconfig.json
!nest-cli.json
!.eslintrc.js
!.prettierrc
!jest.config.js
!docker-compose.yml
!Dockerfile
!prisma/schema.prisma
!.github/workflows/*.yml

# Keep example/template files
!src/**/*.example.ts
!config/*.example.json
